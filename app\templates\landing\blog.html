{% extends "main_layout.html" %}

{% block title %}وبلاگ رومینکست - آخرین مقالات و آموزش‌ها{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary">وبلاگ رومینکست</h1>
            <p class="lead text-muted">آخرین مقالات، آموزش‌ها و نکات کاربردی در زمینه تولید محتوا و هوش مصنوعی</p>
            {% if total %}
            <p class="text-muted">
                <i class="fas fa-newspaper me-2"></i>
                {{ total }} مقاله منتشر شده
            </p>
            {% endif %}
        </div>
    </div>

    <div class="row g-4">
        {% if blogs %}
            {% for blog in blogs %}
                <div class="col-sm-6 col-md-4 col-lg-3 mb-4">
                    <a href="{{ url_for('blog.view', slug=blog.slug) }}" class="text-decoration-none">
                        <div class="blog-card h-100 rounded-4 shadow-sm overflow-hidden bg-white">
                            <div class="blog-thumb position-relative">
                                {% if blog.featured_image %}
                                    <img src="{{ blog.featured_image }}" class="img-fluid w-100" alt="{{ blog.title }}">
                                {% else %}
                                    <img src="/static/img/default_blog_thumb.jpg" class="img-fluid w-100" alt="{{ blog.title }}">
                                {% endif %}
                            </div>
                            <div class="blog-content p-3">
                                <h5 class="blog-title fw-bold mb-2 text-dark">{{ blog.title }}</h5>
                                {% if blog.published_at %}
                                <p class="blog-date small text-muted mb-2">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    {{ blog.published_at.strftime('%Y/%m/%d') }}
                                </p>
                                {% endif %}
                                <p class="blog-excerpt text-muted small mb-3">{{ blog.content|striptags|truncate(120) }}</p>
                                <span class="blog-read-more small">
                                    ادامه مطلب <i class="fas fa-arrow-left ms-1"></i>
                                </span>
                            </div>
                            {% if blog.tags %}
                            <div class="blog-footer p-3 pt-0">
                                {% for tag in blog.tags[:3] %}
                                    <span class="badge bg-light text-dark me-1 small">{{ tag }}</span>
                                {% endfor %}
                                {% if blog.tags|length > 3 %}
                                    <span class="badge bg-light text-muted small">+{{ blog.tags|length - 3 }}</span>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </a>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12 text-center py-5">
                <div class="empty-state">
                    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">هنوز مقاله‌ای منتشر نشده است</h3>
                    <p class="text-muted">به زودی مقالات جدید و مفیدی در این بخش منتشر خواهد شد.</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if pages > 1 %}
    <div class="row mt-5">
        <div class="col-12">
            <!-- Pagination Info -->
            <div class="text-center mb-3">
                <p class="text-muted small">
                    صفحه {{ page }} از {{ pages }}
                    ({{ ((page-1) * per_page + 1) }} تا {{ [page * per_page, total]|min }} از {{ total }} مقاله)
                </p>
            </div>

            <nav aria-label="صفحه‌بندی مقالات">
                <ul class="pagination justify-content-center">
                    <!-- First Page -->
                    {% if page > 2 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=1) }}" title="صفحه اول">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Previous Page -->
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page-1) }}" aria-label="صفحه قبل" title="صفحه قبل">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Page Numbers -->
                    {% set start_page = [1, page - 2]|max %}
                    {% set end_page = [pages, page + 2]|min %}

                    {% if start_page > 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}

                    {% for p in range(start_page, end_page + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('landing.blog', page=p) }}">{{ p }}</a>
                    </li>
                    {% endfor %}

                    {% if end_page < pages %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}

                    <!-- Next Page -->
                    {% if page < pages %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page+1) }}" aria-label="صفحه بعد" title="صفحه بعد">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Last Page -->
                    {% if page < pages - 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=pages) }}" title="صفحه آخر">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<style>
    .blog-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
        border: none;
    }

    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }

    .blog-thumb {
        height: 200px;
        overflow: hidden;
    }

    .blog-thumb img {
        transition: transform 0.5s ease;
        height: 100%;
        object-fit: cover;
    }

    .blog-card:hover .blog-thumb img {
        transform: scale(1.05);
    }

    .blog-title {
        font-size: 1rem;
        line-height: 1.4;
        height: 2.8rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
    }

    .blog-excerpt {
        height: 4rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 0.85rem;
    }

    .blog-read-more {
        color: #00A0A0;
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .blog-read-more:hover {
        color: #007a7a;
    }

    .pagination .page-link {
        color: #00A0A0;
        border-color: #dee2e6;
        padding: 0.5rem 0.75rem;
    }

    .pagination .page-link:hover {
        color: #007a7a;
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #00A0A0;
        border-color: #00A0A0;
    }

    .empty-state {
        padding: 3rem 0;
    }

    @media (max-width: 768px) {
        .blog-card {
            margin-bottom: 1.5rem;
        }

        .blog-thumb {
            height: 180px;
        }

        .pagination .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

