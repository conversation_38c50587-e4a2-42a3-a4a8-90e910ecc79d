{% extends "dashboard_layout.html" %}

{% block title %}Blog Dashboard - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Blog Management</h1>
        <a href="{{ url_for('blog.create') }}" class="btn btn-primary">Create New Post</a>
    </div>
    
    {% if blogs %}
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for blog in blogs %}
                                <tr>
                                    <td>{{ blog.title }}</td>
                                    <td>
                                        {% if blog.is_published %}
                                            <span class="badge bg-success">Published</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Draft</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ blog.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('blog.view', slug=blog.slug) }}" class="btn btn-sm btn-outline-primary">View</a>
                                            <a href="{{ url_for('blog.edit', blog_id=blog.id) }}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete('{{ blog.id }}', '{{ blog.title }}')">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Pagination -->
        {% if pages > 1 %}
        <div class="mt-4">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% for p in range(1, pages + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('blog.dashboard', page=p) }}">{{ p }}</a>
                        </li>
                    {% endfor %}
                </ul>
            </nav>
        </div>
        {% endif %}
    {% else %}
        <div class="alert alert-info">
            You haven't created any blog posts yet. <a href="{{ url_for('blog.create') }}">Create your first post</a>.
        </div>
    {% endif %}
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete "<span id="deleteBlogTitle"></span>"?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" action="">
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(blogId, blogTitle) {
        document.getElementById('deleteBlogTitle').textContent = blogTitle;
        document.getElementById('deleteForm').action = "{{ url_for('blog.delete', blog_id='') }}" + blogId;
        
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>
{% endblock %}