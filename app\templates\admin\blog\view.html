{% extends "main_layout.html" %}

{% block title %}{{ blog.title }} - Rominext{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4 fw-bold">{{ blog.title }}</h1>
            <p class="text-muted">
                Published on {{ blog.published_at.strftime('%Y-%m-%d') if blog.published_at else 'Draft' }}
                {% if current_user.is_authenticated and current_user.id == blog.user.id %}
                    <a href="{{ url_for('blog.edit', blog_id=blog.id) }}" class="btn btn-sm btn-outline-primary ms-2">Edit</a>
                {% endif %}
            </p>
            <div>
                {% for tag in blog.tags %}
                    <span class="badge bg-secondary me-1">{{ tag }}</span>
                {% endfor %}
            </div>
        </div>
    </div>
    
    {% if blog.featured_image %}
    <div class="row mb-4">
        <div class="col-12">
            <img src="{{ blog.featured_image }}" class="img-fluid rounded" alt="{{ blog.title }}">
        </div>
    </div>
    {% endif %}
    
    <div class="row">
        <div class="col-12">
            <div class="blog-content">
                {{ blog.content|safe }}
            </div>
        </div>
    </div>
    
    <div class="row mt-5">
        <div class="col-12">
            <a href="{{ url_for('blog.index') }}" class="btn btn-outline-secondary">&larr; Back to Blog</a>
        </div>
    </div>
</div>
{% endblock %}