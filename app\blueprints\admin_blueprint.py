from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, login_user, logout_user, current_user
from functools import wraps
from ..models import User, UserRole
from mongoengine.errors import NotUniqueError, ValidationError, OperationError
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from ..services.translation_service import TranslationService
import os

# Get translation function
def translate(key, default=None):
    return current_app.translation_service.translate(key, default)

# Create blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Make UserRole available to all templates under this blueprint
@admin_bp.context_processor
def inject_user_role():
    return {'UserRole': UserRole}

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            # Store the requested URL for redirecting after login
            next_url = request.url
            return redirect(url_for('admin.login', next=next_url))
        
        if current_user.role != UserRole.ADMIN:
            flash(translate('auth.access_denied'), 'danger')
            return redirect(url_for('admin.login'))
        
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/')
@admin_required
def index():
    """Admin dashboard home"""
    error_message = None
    users_count = 0
    posts_count = 0
    comments_count = 0
    errors_count = 0
    recent_activities = []
    
    try:
        # Get counts for dashboard
        users_count = User.objects.count()
        
        # Try to get other models if they exist
        try:
            from ..models import Post
            posts_count = Post.objects.count()
        except (ImportError, AttributeError):
            pass
            
        try:
            from ..models import Comment
            comments_count = Comment.objects.count()
        except (ImportError, AttributeError):
            pass
            
        try:
            from ..models.log import Log
            errors_count = Log.objects(level='ERROR').count()
            
            # Get recent activities
            recent_logs = Log.objects.order_by('-created_at').limit(5)
            
            for log in recent_logs:
                icon = 'info-circle'
                color = 'primary'
                
                if log.level == 'ERROR':
                    icon = 'exclamation-triangle'
                    color = 'danger'
                elif log.level == 'WARNING':
                    icon = 'exclamation-circle'
                    color = 'warning'
                elif log.level == 'SUCCESS':
                    icon = 'check-circle'
                    color = 'success'
                
                recent_activities.append({
                    'icon': icon,
                    'color': color,
                    'message': log.message[:50] + '...' if len(log.message) > 50 else log.message,
                    'time': log.created_at.strftime('%Y-%m-%d %H:%M')
                })
        except (ImportError, AttributeError, OperationError) as e:
            # Handle MongoDB index error
            if "IndexOptionsConflict" in str(e):
                error_message = "خطا در پایگاه داده: تداخل در ایندکس‌ها. لطفا با مدیر سیستم تماس بگیرید."
            else:
                error_message = f"خطا در دسترسی به لاگ‌ها: {str(e)}"
    
    except Exception as e:
        error_message = f"خطا در بارگذاری داشبورد: {str(e)}"
    
    return render_template('admin/index.html', 
                          users_count=users_count,
                          posts_count=posts_count,
                          comments_count=comments_count,
                          errors_count=errors_count,
                          recent_activities=recent_activities,
                          error_message=error_message)

@admin_bp.route('/users')
@admin_required
def users():
    """Admin users management"""
    try:
        # Get all users
        users = User.objects.all()
        
        # Pass the users to the template
        return render_template('admin/users.html', users=users, UserRole=UserRole)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست کاربران: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/api/admin/users', methods=['GET'])
@admin_required
def api_users():
    """API endpoint for users with filtering and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        role = request.args.get('role', '')
        status = request.args.get('status', '')
        
        # Build query
        query = {}
        
        if search:
            # Search in name or email
            query['$or'] = [
                {'name': {'$regex': search, '$options': 'i'}},
                {'email': {'$regex': search, '$options': 'i'}}
            ]
        
        if role:
            query['role'] = role
        
        if status:
            is_active = status == 'ACTIVE'
            query['is_active'] = is_active
        
        # Get total count for pagination
        total = User.objects(__raw__=query).count()
        total_pages = (total + per_page - 1) // per_page  # Ceiling division
        
        # Get users with pagination
        users = User.objects(__raw__=query).skip((page - 1) * per_page).limit(per_page)
        
        # Format users for response
        formatted_users = []
        for user in users:
            formatted_users.append({
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'is_active': user.is_active if hasattr(user, 'is_active') else True,
                'created_at': user.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': formatted_users,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages
            }
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/admin/users/<user_id>/toggle-status', methods=['POST'])
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        data = request.get_json()
        active = data.get('active', True)
        
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({
                'success': False,
                'error': 'کاربر یافت نشد'
            }), 404
        
        # Update user status
        user.status = UserStatus.ACTIVE if active else UserStatus.SUSPENDED
        user.save()
        
        return jsonify({
            'success': True,
            'message': 'وضعیت کاربر با موفقیت تغییر کرد'
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/users/add', methods=['POST'])
@admin_required
def add_user():
    """Add a new user"""
    try:
        name = request.form.get('name')
        email = request.form.get('email')
        password = request.form.get('password')
        role_value = request.form.get('role', UserRole.CUSTOMER.value)
        is_active = request.form.get('is_active') == 'on'
        
        # Convert string role to enum
        role = UserRole(role_value)
        
        if not name or not email or not password:
            flash(translate('auth.provide_all_fields'), 'danger')
            return redirect(url_for('admin.users'))

        # Check if user already exists
        if User.objects(email=email).first():
            flash(translate('auth.email_exists'), 'danger')
            return redirect(url_for('admin.users'))
        
        # Create new user with is_admin flag
        is_admin = (role == UserRole.ADMIN)
        
        # Create the user
        user = User.create_user(
            email=email,
            password=password,
            name=name,
            is_admin=is_admin,
            is_active=is_active
        )
        
        # For non-default roles (not ADMIN or CUSTOMER), update explicitly
        if role == UserRole.SUPPORT:
            user.role = UserRole.SUPPORT
            user.save()
        
        flash(translate('auth.user_created_success'), 'success')
        return redirect(url_for('admin.users'))
    except ValidationError as e:
        flash(f'{translate("auth.validation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.users'))
    except Exception as e:
        flash(f'{translate("auth.user_creation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.users'))

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    # If already logged in and is admin, redirect to admin index
    if current_user.is_authenticated and current_user.role == UserRole.ADMIN:
        return redirect(url_for('admin.index'))
    
    # If already logged in but not admin, show error
    if current_user.is_authenticated:
        flash(translate('auth.admin_access_denied'), 'danger')
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = request.form.get('remember', False) == 'on'
        
        if not email or not password:
            flash(translate('auth.provide_email_password'), 'danger')
            return render_template('admin/login.html')
        
        try:
            user = User.objects(email=email).first()
            
            if user and user.check_password(password):
                if user.role == UserRole.ADMIN:
                    login_user(user, remember=remember)
                    # Simplified success message
                    flash(translate('auth.welcome'), 'success')
                    
                    # Redirect to the next page if provided
                    next_page = request.args.get('next')
                    if next_page:
                        return redirect(next_page)
                    return redirect(url_for('admin.index'))
                else:
                    flash(translate('auth.admin_access_denied'), 'danger')
            else:
                flash(translate('auth.invalid_credentials'), 'danger')
        except Exception as e:
            flash(f'{translate("auth.login_error")}: {str(e)}', 'danger')
    
    return render_template('admin/login.html')

@admin_bp.route('/logout')
@login_required
def logout():
    """Admin logout"""
    logout_user()
    flash(translate('auth.logout_success'), 'success')
    return redirect(url_for('admin.login'))

@admin_bp.route('/posts')
@admin_required
def posts():
    """Admin posts management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search = request.args.get('search', '')
        platform = request.args.get('platform', '')
        status = request.args.get('status', '')
        
        # Import Post model
        from ..models import Post
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if platform and platform != 'همه':
            query['platform'] = platform
        
        if status:
            if status == 'published':
                query['published'] = True
                query['scheduled'] = False
            elif status == 'scheduled':
                query['scheduled'] = True
            elif status == 'draft':
                query['published'] = False
                query['scheduled'] = False
        
        # Get total count for pagination
        total_posts = Post.objects(__raw__=query).count()
        total_pages = (total_posts + per_page - 1) // per_page  # Ceiling division
        
        # Get posts with pagination
        posts = Post.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/posts.html', 
                              posts=posts,
                              total_posts=total_posts,
                              total_pages=total_pages,
                              current_page=page)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست پست‌ها: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/api/admin/posts', methods=['GET'])
@admin_required
def api_posts():
    """API endpoint for posts with filtering and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        platform = request.args.get('platform', '')
        status = request.args.get('status', '')
        
        # Import Post model
        from ..models import Post
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if platform and platform != 'همه':
            query['platform'] = platform
        
        if status:
            if status == 'published':
                query['published'] = True
                query['scheduled'] = False
            elif status == 'scheduled':
                query['scheduled'] = True
            elif status == 'draft':
                query['published'] = False
                query['scheduled'] = False
        
        # Get total count for pagination
        total = Post.objects(__raw__=query).count()
        total_pages = (total + per_page - 1) // per_page  # Ceiling division
        
        # Get posts with pagination
        posts = Post.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        # Format posts for response
        formatted_posts = []
        for post in posts:
            formatted_posts.append({
                'id': str(post.id),
                'title': post.title if hasattr(post, 'title') else '',
                'content': post.content,
                'platform': post.platform,
                'published': post.published if hasattr(post, 'published') else False,
                'scheduled': post.scheduled if hasattr(post, 'scheduled') else False,
                'publish_date': post.publish_date.isoformat() if hasattr(post, 'publish_date') and post.publish_date else None,
                'likes': post.analytics_data.get('likes', 0) if hasattr(post, 'analytics_data') else 0,
                'comments': post.analytics_data.get('comments', 0) if hasattr(post, 'analytics_data') else 0,
                'created_at': post.created_at.isoformat() if hasattr(post, 'created_at') else None
            })
        
        return jsonify({
            'success': True,
            'data': formatted_posts,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages
            }
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/posts/delete/<post_id>', methods=['GET'])
@admin_required
def delete_post(post_id):
    """Delete a post"""
    try:
        from ..models import Post
        post = Post.objects(id=post_id).first()
        
        if not post:
            flash(translate('auth.post_not_found'), 'danger')
            return redirect(url_for('admin.posts'))

        # Delete the post
        post.delete()

        flash(translate('auth.post_deleted_success'), 'success')
        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_delete_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/posts/publish/<post_id>', methods=['GET'])
@admin_required
def publish_post(post_id):
    """Publish a post"""
    try:
        from ..models import Post
        post = Post.objects(id=post_id).first()
        
        if not post:
            flash(translate('auth.post_not_found'), 'danger')
            return redirect(url_for('admin.posts'))

        # Update post status
        post.published = True
        post.scheduled = False
        post.publish_date = datetime.utcnow()
        post.save()

        flash(translate('auth.post_published_success'), 'success')
        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_publish_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/posts/add', methods=['POST'])
@admin_required
def add_post():
    """Add a new post"""
    try:
        title = request.form.get('title')
        content = request.form.get('content')
        platform = request.form.get('platform')
        status = request.form.get('status')
        schedule_date = request.form.get('schedule_date')
        
        # Handle image upload
        image = None
        if 'image' in request.files and request.files['image'].filename:
            image_file = request.files['image']
            # Process and save image
            # This is a placeholder - implement actual image saving logic
            image = f"uploads/{secure_filename(image_file.filename)}"
            # image_file.save(os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(image_file.filename)))
        
        # Create post object
        try:
            from ..models import Post
            post = Post(
                title=title,
                content=content,
                platform=platform,
                published=(status == 'published'),
                scheduled=(status == 'scheduled'),
                publish_date=datetime.fromisoformat(schedule_date) if schedule_date else None,
                image=image,
                created_by=current_user.id
            )
            post.save()
            flash(translate('auth.post_created_success'), 'success')
        except (ImportError, AttributeError):
            # If Post model doesn't exist
            flash(f'{translate("auth.post_created_success")} (حالت نمایشی)', 'success')

        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_creation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/logs')
@admin_required
def logs():
    """Admin logs management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 20
        level = request.args.get('level', '')
        category = request.args.get('category', '')
        
        # Import Log model
        from ..models.log import Log, LogLevel
        
        # Build query
        query = {}
        
        if level and level != 'all':
            query['level'] = level
            
        # Get total count for pagination
        total_logs = Log.objects(**query).count()
        total_pages = (total_logs + per_page - 1) // per_page  # Ceiling division
        
        # Get logs with pagination
        logs = Log.objects(**query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/logs.html', 
                              logs=logs,
                              LogLevel=LogLevel,
                              total_logs=total_logs,
                              total_pages=total_pages,
                              current_page=page,
                              current_level=level)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست لاگ‌ها: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

# Add blog management routes to admin blueprint
@admin_bp.route('/blogs')
@admin_required
def blogs():
    """Admin blogs management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search = request.args.get('search', '')
        status = request.args.get('status', '')
        
        # Import Blog model
        from ..models import Blog
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if status:
            if status == 'published':
                query['is_published'] = True
            elif status == 'draft':
                query['is_published'] = False
        
        # Get total count for pagination
        total_blogs = Blog.objects(__raw__=query).count()
        total_pages = (total_blogs + per_page - 1) // per_page  # Ceiling division
        
        # Get blogs with pagination
        blogs = Blog.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/blogs.html', 
                              blogs=blogs,
                              total_blogs=total_blogs,
                              total_pages=total_pages,
                              current_page=page)
    except Exception as e:
        flash(f'Error loading blog list: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/blogs/create', methods=['GET', 'POST'])
@admin_required
def create_blog():
    """Create a new blog post"""
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    if request.method == 'POST':
        try:
            # Validate request data
            title = request.form.get('title')
            content = request.form.get('content')
            tags = request.form.get('tags', '')
            tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            is_published = 'is_published' in request.form
            
            # Handle featured image
            featured_image = None
            if 'featured_image' in request.files and request.files['featured_image'].filename:
                image_file = request.files['featured_image']
                featured_image = f"uploads/{secure_filename(image_file.filename)}"
                # TODO: Implement actual file saving logic
            
            # Create blog
            blog = blog_service.create_blog(
                user_id=current_user.id,
                title=title,
                content=content,
                tags=tags_list,
                featured_image=featured_image,
                is_published=is_published
            )
            
            flash(translate('auth.blog_created_success'), 'success')
            return redirect(url_for('admin.blogs'))

        except Exception as e:
            flash(f'{translate("auth.blog_creation_error")}: {str(e)}', 'danger')
    
    return render_template('admin/blog_create.html')

@admin_bp.route('/blogs/edit/<blog_id>', methods=['GET', 'POST'])
@admin_required
def edit_blog(blog_id):
    """Edit a blog post"""
    from ..models import Blog
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    try:
        blog = Blog.objects.get(id=blog_id)
        
        if request.method == 'POST':
            try:
                # Get form data
                title = request.form.get('title')
                content = request.form.get('content')
                tags = request.form.get('tags', '')
                tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                is_published = 'is_published' in request.form
                
                # Handle featured image
                featured_image = blog.featured_image
                if 'featured_image' in request.files and request.files['featured_image'].filename:
                    image_file = request.files['featured_image']
                    featured_image = f"uploads/{secure_filename(image_file.filename)}"
                    # TODO: Implement actual file saving logic
                
                # Update blog
                updated_blog = blog_service.update_blog(
                    blog_id=blog_id,
                    title=title,
                    content=content,
                    tags=tags_list,
                    featured_image=featured_image,
                    is_published=is_published
                )
                
                flash(translate('auth.blog_updated_success'), 'success')
                return redirect(url_for('admin.blogs'))

            except Exception as e:
                flash(f'{translate("auth.blog_update_error")}: {str(e)}', 'danger')
        
        return render_template('admin/blog_edit.html', blog=blog)
    except Exception as e:
        flash(f'Error editing blog: {str(e)}', 'danger')
        return redirect(url_for('admin.blogs'))

@admin_bp.route('/blogs/delete/<blog_id>', methods=['POST'])
@admin_required
def delete_blog(blog_id):
    """Delete a blog post"""
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    try:
        # Delete blog
        blog_service.delete_blog(blog_id)
        
        flash(translate('auth.blog_deleted_success'), 'success')
        return redirect(url_for('admin.blogs'))
    except Exception as e:
        flash(f'{translate("auth.blog_delete_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.blogs'))

# Helper function for mock data
def get_mock_posts():
    """Get mock posts data for development"""
    return [
        {
            'id': 1,
            'title': 'معرفی محصول جدید',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'instagram',
            'published': True,
            'scheduled': False,
            'publish_date': datetime.now(),
            'likes': 120,
            'comments': 14,
            'image': 'https://via.placeholder.com/300'
        },
        {
            'id': 2,
            'title': 'اطلاعیه مهم',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'telegram',
            'published': False,
            'scheduled': True,
            'publish_date': datetime.now() + timedelta(days=2),
            'likes': 0,
            'comments': 0,
            'image': None
        },
        {
            'id': 3,
            'title': 'نکات مهم',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'twitter',
            'published': True,
            'scheduled': False,
            'publish_date': datetime.now(),
            'likes': 50,
            'comments': 10,
            'image': 'https://via.placeholder.com/300'
        }
    ]
